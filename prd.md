好的，我们立即基于这次至关重要的方向校准，撰写一份全新的、聚焦于“AI伴读笔记”核心体验的MVP产品需求文档（PRD）。

这份PRD将更加精炼，所有细节都将围绕**“一个输入，一份伴读笔记，一个整体”**的核心理念展开。

---

### **产品需求文档 (PRD): Augmented Reader MVP v2.0**

**版本:** 2.0
**日期:** 2025年6月10日
**作者:** (您的名字)
**状态:** 定稿，待开发

#### **1. 产品概述 (Overview)**

**Augmented Reader** 是一款智能阅读与笔记工具。它旨在通过AI技术，为用户输入的每一份文档（文本或网页链接）自动生成一份高度结构化的、可交互的“AI伴读笔记”，帮助用户在极短时间内理解核心内容，并支持将“原文+笔记”的组合一键存入个人知识库，最终通过FastGPT进行高效的知识调用。

#### **2. 核心目标 (Core Objectives for MVP)**

1.  **实现核心工作流:** 验证“输入 -> AI生成伴读笔记 -> 用户交互与保存 -> 同步至FastGPT”这一核心价值链条。
2.  **打造“Aha!”时刻:** 让用户在首次使用时，能被“AI伴读笔记”的深度和“笔记-原文”联动的便捷性所震撼。
3.  **技术可行性验证:** 跑通网页抓取、AI调用、本地存储、外部服务集成等关键技术环节。

#### **3. 目标用户 (Target User)**

**核心一号用户:** 习惯于在阅读后，借助AI生成结构化笔记，并以此为索引回溯原文进行深度理解的知识工作者。

#### **4. 功能范围与需求详述 (Features & Requirements)**

**史诗 1: 核心阅读与笔记工作台 (The Core Workbench)**

> **用户故事:** 作为一个用户，我希望能粘贴文本或URL，然后在一个统一的界面上，同时看到原文和AI为我生成的、结构化的“伴读笔记”，并且笔记和原文可以交互。

| 需求ID | 需求描述 | 优先级 | 技术实现要点 |
| :--- | :--- | :--- | :--- |
| **F-1.1** | **支持文本和URL输入** | **Must Have** | 在主界面提供一个统一的输入框，能智能识别输入是纯文本还是URL。 |
| **F-1.2** | **获取并解析内容** | **Must Have** | **后端:** 实现一个统一的处理模块。对URL，使用`readability`+`Playwright`抓取正文；对文本，直接使用。 |
| **F-1.3** | **生成唯一的“AI伴读笔记”** | **Must Have** | **后端:** 设计一个包含固定结构（摘要、大纲、要点等）的Prompt，调用LLM，返回一份结构化的Markdown。 |
| **F-1.4** | **实现两栏式布局界面** | **Must Have** | **前端:** 左侧主体区用于显示原文，右侧侧边栏用于显示“AI伴读笔记”。 |
| **F-1.5** | **渲染原文与笔记** | **Must Have** | **前端:** 左侧使用`typography`插件美化渲染原文；右侧使用Markdown渲染器展示“AI伴读笔记”。 |
| **F-1.6** | **实现笔记到原文的跳转** | **Should Have**| **核心交互:** 需要后端在生成笔记时，建立笔记章节与原文位置的映射。前端实现点击笔记中的大纲标题，左侧原文自动滚动到对应位置。 |
| **F-1.7** | **实现“圈选-提问”交互** | **Could Have**| **增强交互:** 在伴读笔记中，实现划选文本后弹出快捷菜单（“提问”、“展开”），并能与下方的AI问答窗口联动。 |
| **F-1.8** | **实现上下文AI问答窗口** | **Must Have** | **前后端:** 在侧边栏下方提供一个聊天窗口。用户提问时，将“原文”和“伴读笔记”作为上下文，调用LLM进行回答。 |

**史诗 2: 知识沉淀与外部集成 (Knowledge Persistence & Integration)**

> **用户故事:** 作为一个用户，当我完成一次阅读并对结果满意后，我希望能一键保存这次会话（原文+笔记），并能将我所有保存过的知识同步到FastGPT，以便随时提问。

| 需求ID | 需求描述 | 优先级 | 技术实现要点 |
| :--- | :--- | :--- | :--- |
| **F-2.1** | **定义本地存储结构** | **Must Have** | **数据库:** 使用SQLite + Prisma。设计一个`ReadingSession`表，字段包含`id`, `sourceType`, `sourceData`, `originalContent`, `aiNoteMarkdown`。 |
| **F-2.2** | **实现“保存会话”功能** | **Must Have** | **前后端:** 在界面上提供一个清晰的“保存”按钮。点击后，将当前会话的所有数据（原文、笔记等）作为一个整体，调用API存入本地SQLite数据库。 |
| **F-2.3** | **实现历史会话列表** | **Should Have**| **前后端:** 在界面某个位置（如可抽拉的左侧面板）显示所有已保存的`ReadingSession`列表，用户可以点击加载之前的阅读会话。 |
| **F-2.4** | **实现与FastGPT的同步** | **Must Have** | **后端:** 创建一个同步API。该API会查询本地数据库中所有已保存的会话，将每条会话的“原文+笔记”内容，合并处理后，推送给指定的FastGPT知识库。 |
| **F-2.5** | **提供手动同步触发器** | **Must Have** | **前端:** 在UI上提供一个“同步”按钮，用户点击后触发后端的同步API，并显示同步状态。 |
| **F-2.6** | **集成FastGPT对话入口**| **Must Have** | **前端:** 提供一个入口，点击后跳转到一个新页面或打开一个模态框，里面嵌入FastGPT的对话界面。 |

#### **5. 非功能性需求 (Non-Functional Requirements)**

| 需求ID | 需求描述 | 优先级 |
| :--- | :--- | :--- |
| **NF-1** | **性能:** 从输入到笔记呈现，对于中等长度的文章，耗时应控制在10秒以内。 | **Should Have**|
| **NF-2** | **用户体验:** 界面设计需保持简洁、直观，遵循之前定义的视觉风格。 | **Must Have** |
| **NF-3** | **部署:** 应用需能被轻松部署，前端(Vercel)和后端API(Next.js集成)一体化。| **Must Have** |

#### **6. MVP发布标准 (MVP Release Criteria)**

*   **史诗1** 中的所有 **Must Have** 功能已完成并通过测试。
*   **史诗2** 中的所有 **Must Have** 功能已完成并通过测试。
*   应用已成功部署，并能在主流浏览器上稳定运行。
*   核心流程“输入->生成笔记->保存->同步->对话”已完全跑通，无阻塞性BUG。

---

这份PRD文档为您提供了一个清晰、聚焦且可执行的开发蓝图。它紧密围绕着您最新的、更精准的产品构想，确保了MVP的每一项工作都是在为最终的核心体验添砖加瓦。